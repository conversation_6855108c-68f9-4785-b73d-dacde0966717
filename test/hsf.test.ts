import { createFunctionApp, close } from '@midwayjs/mock';
import { Framework, Application } from '@ali/serverless-app';
import { ShortLinkHandler } from '../src/short-link';
import { join } from 'path';

describe('test/hsf.test.ts', () => {
  let app: Application;

  beforeAll(async () => {
    // 主动设置测试环境，ctx.env获取的是daily环境，没法区分
    process.env.UNITTEST = 'true';
    
    // 创建app
    const cwd = join(__dirname, '../');
    app = await createFunctionApp<Framework>(cwd);
  });

  afterAll(async () => {
    await close(app);
  });

  it('短链生成', async () => {
    const instance: ShortLinkHandler = await app.getServerlessInstance(ShortLinkHandler);
    const result = await instance.handler({ longLink: 'https://market.m.taobao.com/app/trip/rx-home/pages/home' });
    expect(result.success).toEqual(true);
  });
});
