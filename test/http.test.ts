import { createFunctionApp, close, createHttpRequest } from '@midwayjs/mock';
import { Framework, Application } from '@ali/serverless-app';
import { join } from 'path';

describe('test/http.test.ts', () => {
  let app: Application;

  beforeAll(async () => {
    // 之所以延迟执行是因为多线程的一个bug
    // 简单来说就是src/index.ts的AirClient初始化的时候，会报fs.mkdirSync的错
    // 但实际在此之前已经做了fs.existsSync检测，因此推测是test目前下的文件会被多线程执行导致的
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 主动设置测试环境，ctx.env获取的是daily环境，没法区分
    process.env.UNITTEST = 'true';
    
    // 创建app
    const cwd = join(__dirname, '../');
    app = await createFunctionApp<Framework>(cwd);
  });

  afterAll(async () => {
    await close(app);
  });

  it('校验文件代理', async () => {
    const result = await createHttpRequest(app).get('/hdNVvhWidw.txt');
    expect(result.text).toEqual('a9de50b8b1bdb5f411709c6f6bf388e7');
    const result2 = await createHttpRequest(app).get('/3963d0f6bc816cc316e5028d4455f53e.html');
    expect(result2.text).toEqual('3963d0f6bc816cc316e5028d4455f53e');
  });

  it('静态资源代理', async () => {
    const result = await createHttpRequest(app).get('/proxy').query({
      url: 'https://gw.alicdn.com/imgextra/i2/O1CN01UPnxmp28OKQIapeh8_!!6000000007922-2-tps-224-54.png_230x10000.jpg'
    })
    expect(result.text).toMatch(/^\<\!DOCTYPE html\>/);
  });

  it('页面代理', async () => {
    const result = await createHttpRequest(app).get('/app/trip/rx-home/pages/home');
    expect(result.text.trim()).toMatch(/^\<\!DOCTYPE html\>/);
  });
});
