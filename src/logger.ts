import {
  Inject,
  Provide,
  ServerlessFunction,
  ServerlessTrigger,
  ServerlessTriggerType,
} from '@midwayjs/decorator';
import { Context } from '@midwayjs/faas';
import urllib from 'urllib';

const ARMS_REQ_PATH = 'https://arms-retcode.aliyuncs.com/r.png';
const MAX_LOG_COUNT = 4; // 不全部取样，随机取4条，降低成本

function getRandomNumbers(min, max, count) {
  let numbers = new Set();

  while (numbers.size < count) {
    let randomNumber = Math.floor(Math.random() * (max - min + 1)) + min;
    numbers.add(randomNumber);
  }
  return [...numbers];
}

// 获取实际上报的数据-限制条数场景
function getReportList(list: Array<Record<string, any>>) {
  const len = list.length;
  if (len <= MAX_LOG_COUNT) return list;
  const randomIndex = getRandomNumbers(0, len - 1, MAX_LOG_COUNT);
  return randomIndex.map((idx: number) => list[idx]);
}

@Provide()
export class WxLoggerHandler {
  __timestamp = Date.now();

  @Inject()
  ctx: Context;

  @Inject()
  serviceManager;

  /**
   * @param event
   */
  @ServerlessFunction({ functionName: 'batch-logger' })
  @ServerlessTrigger(ServerlessTriggerType.HSF)
  async sendRequestLogger(event: any = {}): Promise<any> {
    const { list, ...commonParams } = event;
    if (!list || !Array.isArray(list) || !list.length) {
      return {
        msg: '请求参数错误',
      };
    }

    const urlObj = new URL(ARMS_REQ_PATH);
    // 拼接通用参数
    Object.keys(commonParams).forEach((qKey) => {
      urlObj.searchParams.set(qKey, commonParams[qKey]);
    });
    const armsPath = urlObj.toString();
    // 发出请求，落入日志
    const reqList = getReportList(list).map((item) => {
      const apiQueryStr = Object.keys(item).map(v => `${v}=${item[v]}`).join('&');
      const reqUrl = `${armsPath}&${apiQueryStr}`;
      urllib.request(reqUrl, { headers: this.ctx.headers });
      return reqUrl;
    });
    return { msg: 'ok', reqList };
  }
}
