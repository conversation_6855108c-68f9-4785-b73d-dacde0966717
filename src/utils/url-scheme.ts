
const PAGE_PATH = '/app/axintrip/h5-axdist/pages/r/index.html';

export function needRequestScheme(path: string) {
  return path === PAGE_PATH;
}

const PATH_MAP = {
  '/pages/home/<USER>': '/pages/main/home',
  '/pages/utils/webview/index': '/pages/main/webview'
}

function getRealPath(path) {
  // 兜底酒店（老逻辑）
  if (!path) {
    return '/pages/hotel/detail/index';
  }
  // 新页面
  const fullPath = path.startsWith('/') ? path : `/${path}`;
  const newPath = PATH_MAP[fullPath];
  if (newPath) {
    return newPath;
  }
  return path;
}

/**
 * 逻辑来源：http://gitlab.alibaba-inc.com/axintrip/h5-axdist
 * src/pages/r/containers/App.jsx
 */
export function getSchemeRequestParams(query) {
  const { path, ...others } = query;
  return {
    path: getRealPath(path),
    query: Object.keys(others).reduce((pre, cur) => {
      const symbol = pre ? '&' : '';
      return `${pre}${symbol}${cur}=${encodeURIComponent(others[cur])}`;
    }, ''),
  };
}

export function getUrlSchemeScript(urlScheme: string) {
  return `<script>window.__wxUrlScheme = '${urlScheme}';</script>`
}