export const DOMAINS = [
  /**
   * 生产域名
   */
  'h5.m.taobao.com',
  'market.m.taobao.com',
  'f.m.taobao.com',
  'ai.alimebot.taobao.com',
  'files.alicdn.com',


  /**
   * 预发域名
   */
  'h5.wapa.taobao.com',
  'market.wapa.taobao.com',
  'pre-wormhole.wapa.taobao.com',
  'wapp.wapa.taobao.com',
  'edith.wapa.taobao.com'
];

export const PRO = {
  '/app': 'market.m.taobao.com',
  '/trip': 'h5.m.taobao.com',
  '/wow': 'f.m.taobao.com',
  '/xj': 'outfliggys.m.taobao.com',
  '/xj-csr': 'outfliggys.m.taobao.com',
  '/item': 'detail.m.tmall.com',
  '/tpsservice': 'files.alicdn.com',
  '/apps': 'survey.nhcilab.com',
  '/html': 'haibao.m.taobao.com',
  '/clam': 'edith.m.taobao.com',
  '/markets': 'market.m.taobao.com',
  '/.well-known': 'market.wapa.taobao.com',
}
export const PRE = {
  '/app': 'market.wapa.taobao.com',
  '/trip': 'h5.wapa.taobao.com',
  '/wow': 'pre-wormhole.wapa.taobao.com',
  '/xj': 'outfliggys.wapa.taobao.com',
  '/xj-csr': 'outfliggys.wapa.taobao.com',
  '/item': 'detail.m.tmall.com',
  '/tpsservice': 'files.alicdn.com',
  '/proxy': 'edith.wapa.taobao.com',
  '/apps': 'survey.nhcilab.com',
  '/html': 'haibao.m.taobao.com',
  '/clam': 'edith.wapa.taobao.com',
  '/.well-known': 'market.wapa.taobao.com',
}

export const SSR_HOST_ARR = [
  'pre-fliggyrax.wapa.taobao.com',
  'pre-fliggyssr.wapa.taobao.com',
  'outfliggys.m.taobao.com',
  'trip-air-fliggy-common-er.pre-air-er.taobao.com',
  'outfliggys.wapa.taobao.com'
]

export const PRE_HOST_NAMES = [
  'pre-proxy.feizhu.com',
  'pre-proxy.fzwxxcx.com',
  'pre-proxy.fzwxxcx.cn',
  'pre-d.fzwxxcx.com',
  'pre-proxy.feitianzhu.com',
];

export const PRO_HOST_NAMES = [
  'proxy.feizhu.com',
  'proxy.fzwxxcx.com',
  'proxy.fzwxxcx.cn',
  'd.fzwxxcx.com',
  's.feizhu.com',
  'proxy.feitianzhu.com',
];


/**
 * JS接口安全域名
 */
export const JS_DOMAIN = {
  '3963d0f6bc816cc316e5028d4455f53e.html': '3963d0f6bc816cc316e5028d4455f53e',
  'MP_verify_K5Z9rJO3AROzSTZV.txt': 'K5Z9rJO3AROzSTZV', // 微信公众号-？
  'MP_verify_mTn5RvAWGWqoah97.txt': 'mTn5RvAWGWqoah97', // 微信公众号-？
  // https://aone.alibaba-inc.com/v2/project/1073979/req/53075245
  'MP_verify_YEP7kcsYOIzLuJ5Y.txt': 'YEP7kcsYOIzLuJ5Y', // 微信公众号-jsapi支付
  'MP_verify_nePPUsWYLfK8mjVO.txt': 'nePPUsWYLfK8mjVO'
}

/**
 * 业务安全域名（套壳）+扫码安全域名
 */
export const BIZ_DOMAIN = {
  'hdNVvhWidw.txt': 'a9de50b8b1bdb5f411709c6f6bf388e7', // 微信小程序-飞猪订酒店机票火车汽车门票
  'oM9ACJF35l.txt': '2702d8edba61193773f481dcf145ca37', // 微信小程序-码上游玩
  '9274592929.txt': '8a3c9083257e3f54a369bd61cbe0a3a2', // 微信小程序-饿了么买药
  'CIQU1gqrqf.txt': '7c41ec64b46dced227dbd9d07c2bfc79', // 微信小程序-阿信
  '6cXGtD0fRD.txt': '768671b06b4d39f2d33bd66e2773fafd', // 微信小程序-猜测阿信
  'JAkBtye34a.txt': '55d867811491baf4850672d573edc242',
  'JYJKcLO059.txt': '32cae23abdb258d3eeaa0f93bd5d2585',
  'vT6017gDTI.txt': '490bcc98a04fcd764b3d78fdd007fd0e',
  'VbghXrYYMs.txt': 'c02ae118f724953b91dc8514249372df',
  'NDg0lzYABx.txt': 'c37e5461e9f0b67d9c8d07660c860f11', // 抖音小程序-线上
  'NDtFpyEBQf.txt': '768bcecdbee534f46178a517d7954fd0', // 抖音小程序-预发
  '1KYKqVUq5V.txt': '4ccc501c4375dcc9d1f6e3ae7a42fa98',
  'vLfDio4Hma.txt': '53ac65c36b304804a8af4797fd535842',
  'nIxpzymT3r.txt': 'b20275eac5de0b2e023161c10efa77c9',
  'g8ToAzTdYJ.txt': 'c784b4d38fe0a82b1a685df8abafb098', // 阿里商旅
  'o67Q2aVCRn.txt': '12fbf1f2a9daad61e3e9799297b45502', // 杭州文旅
  'fq4NwrrXZP.txt': '0f5cbfcdccf74bfd033e5ea0a0239b1e', //微信小程序-飞猪机票预订特价国际随心飞盲盒
  '1f8f699f.txt': '1f8f699ff18f3575e6', // 小红书小程序
  '8830ab69.txt': '8830ab69058ae1e0b5', // 新小红书小程序
  'UFnDaqjFnf.txt': 'c704e4fa867ff6dee307e1a02f6756d6', // 深圳文旅
  'OAIzCiTK3E.txt': 'ad6378468fd3de01336ab993203eddd9', // 移动全球通
  'HOENmkfG4d.txt': '1cc8eaf68dac6b5c67c0bbc7e91f91c9', // 广东移动小程序
  'NvdeX09Nxc.txt': 'fc98c6273c0d7815156fc65507f99bab', // 移动小程序
  'MTMWfobA5L.txt': '192f173fd1079a97d801ed146b2fd7f0', // 商家微信小程序
  'JDwIAQJuIr.txt': '484373fe432e5a27719df9a862fc6949', // @织火
  'lP0DUcPM36.txt': '25ce3b9913bb9cfd4a294442b10adbf1', // 菜鸟小程序
  'b54af1bf84c4f5a4925b1561ed7d88bb.html': 'b54af1bf84c4f5a4925b1561ed7d88bb' // 支付宝三方小程序 @溪童
}

export const NO_OVERRIDE_HEADERS = ['content-encoding', 'eagleeye-traceid'];

// 图片的后缀名
export const IMAGE_EXTENSION = ['.ico', '.png', '.jpg', '.jpeg', '.gif', '.bmp', '.webp', '.svg'];

// 账号登出域名。passport.feizhu.com并不会清除cookie中的munb信息，导致判断登录异常，因此走代理清除
export const PASSPORT_LOGOUT_INFO = {
  HOST: [
    'passport.feizhu.com',
    'passport.fzwxxcx.com',
    'passport.fzwxxcx.cn',
    'passport.tmwxxcx.com',
    'pre-passport.feizhu.com',
  ],
  PATH: '/logout.htm'
};

// 页匠页path
export const PCRAFT_PATH_INFO = {
  path: '/wow',
  queryKey: 'wh_pid',
}

// scrm域名
export const SCRM_HOST = ['scrm.feizhu.com', 'pre-scrm.feizhu.com'];

// 生产允许代理的域名 -- 外网域名不允许访问内网域名
export const PRO_DOMAIN_WHITELIST = [
  'outfliggys.m.taobao.com', // SSR域名
  'ssr.m.fliggy.com', // SSR域名
  'f.m.fliggy.com', // CSR域名
  'market.m.taobao.com', // 常用域名
  'h5.m.taobao.com',
  'f.m.taobao.com',
  'ai.alimebot.taobao.com',
  'detail.m.tmall.com',
  'files.alicdn.com',
  'survey.nhcilab.com',
  'edith.m.taobao.com',
  'haibao.m.taobao.com',
  'scrm.feizhu.com', // scrm域名
  'passport.feizhu.com',  // 登录域名
  'passport.fzwxxcx.com',  // 登录域名
  'passport.fzwxxcx.cn',  // 登录域名
  'passport.tmwxxcx.com',  // 登录域名
  'survey.taobao.com' //问卷域名
];

// 代理三方router插入广告header script
export const AD_HEADER_SCRIPT = `<script>
  var alitripBridge = window['methodChannel'];
  var NAVIGATOR = window.navigator || {};
  var ua = NAVIGATOR.userAgent || NAVIGATOR.swuserAgent || '';
  var isDebug = window.location.href.includes('client_env=debug');
  var isRelease = window.location.href.includes('client_env=release');
  var isAndroid = ua.toLowerCase().includes('android');
  var isIOS = ua.toLowerCase().includes('iphone');
  var placementId = isAndroid ? "b68381bc2d9bee" : "b683806773b03d";
  if (isAndroid) {
    if (isDebug || isRelease) {
      placementId = "b68305a2390625"
    }
  } else {
    if (isDebug) {
      placementId = "b683451c168909"
    } else if (isRelease) {
      placementId = "b1ggvt6k20u30l"
    }
  }
  var utLogParams = {
    "cna": "dmq0IKAcWG0CASp4SwFerwFt",
    "jsver": "aplus.js",
    "_bridge_version": "2.0.0",
    "pver": "1.0.0",
    "functype": "page",
    "page_type": "h5",
    "utver": "3.0.48",
    "fail_callback": "toUT_fail_13",
    "page_name": window.location.href,
    "lver": "1.9.14",
    prism_lk: '',
    _h5params: encodeURIComponent(location.search && location.search.slice(1)),
    _h5hash: encodeURIComponent(location.hash && location.hash.slice(1)),
    funcId: '2001',
    keepword: 1,
    eventId: '2001',
    event: 'PAGE',
    _bridgeName: 'TripBridge',
    'spm-cnt': '181.30248593.0.0', // 当前 spm
    'spm-url': '181.29754530.0.0',
    'spm-pre': '',
    _h5url: window.location.href,
    url: window.location.href,
    _ish5: '1',
    _toUT: 2,
  };
  function getLogParams(logParams) {
    return {
      gmkey: 'CLK',
      gokey: encodeURIComponent(JSON.stringify({ isAndroid: isAndroid, isIOS: isIOS })),
      spm: '181.30248593.0.0',
      functype: 'ctrl',
      funcId: '2101',
      pageName: window.location.href,
      eventId: '2101',
      event: 'CLK',
      _bridgeName: 'TripBridge',
      logkey: logParams.logkey,
      'spm-cnt': '181.30248593.0.0',
      'spm-url': '181.29754530.0.0',
      'spm-pre': '',
      _h5url: window.location.href,
      url: window.location.href,
      _ish5: '1',
      _toUT: 2,
    }
  };
  if (alitripBridge && (isAndroid || isIOS)) {
    // 页面埋点
    alitripBridge.call('toUT', utLogParams);
    // 初始化taku sdk
    alitripBridge.call('taku_ad_plugin', {
      action: "initTakuSDK",
      personalized: true,
      denied_upload_info: []
    }, function (ret) {
      alitripBridge.call('toUT', getLogParams({
        logkey: '/tbtrip.taku-sdk.init'
      }));
      // 广告预加载
      alitripBridge.call('taku_ad_plugin', {
        action: "loadInterstitialAD",
        placement_id: placementId,
        open_uid: '',
        server_data: ''
      }, function (ret) {
        alitripBridge.call('toUT', getLogParams({
          logkey: '/tbtrip.taku-sdk.preload'
        }));
      }, function (e) {
        alitripBridge.call('toUT', getLogParams({
          logkey: '/tbtrip.taku-sdk.preloaderror'
        }));
      });
    }, function (e) {
      alitripBridge.call('toUT', getLogParams({
        logkey: '/tbtrip.taku-sdk.initerror'
      }));
    });
  }
  </script>`;

// 代理三方router插入广告body script
export const AD_BODY_SCRIPT = `<script>
  var showFirstAd = false;
  var canShowAd = false;
  var alitripBridge = window['methodChannel'];
  var ua = NAVIGATOR.userAgent || NAVIGATOR.swuserAgent || '';
  var isDebug = window.location.href.includes('client_env=debug');
  var isRelease = window.location.href.includes('client_env=release');
  var isAndroid = ua.toLowerCase().includes('android');
  var isIOS = ua.toLowerCase().includes('iphone');
  var placementId = isAndroid ? "b68381bc2d9bee" : "b683806773b03d";
  if (isAndroid) {
    if (isDebug || isRelease) {
      placementId = "b68305a2390625"
    }
  } else {
    if (isDebug) {
      placementId = "b683451c168909"
    } else if (isRelease) {
      placementId = "b1ggvt6k20u30l"
    }
  }
  function getLogParams(logParams) {
    return {
      gmkey: 'CLK',
      gokey: encodeURIComponent(JSON.stringify({ isAndroid: isAndroid, isIOS: isIOS })),
      spm: '181.30248593.0.0',
      functype: 'ctrl',
      funcId: '2101',
      pageName: window.location.href,
      eventId: '2101',
      event: 'CLK',
      _bridgeName: 'TripBridge',
      logkey: logParams.logkey,
      'spm-cnt': '181.30248593.0.0',
      'spm-url': '181.29754530.0.0',
      'spm-pre': '',
      _h5url: window.location.href,
      url: window.location.href,
      _ish5: '1',
      _toUT: 2,
    }
  };
  document.addEventListener('visibilitychange', function (e) {
    const status = e.detail && e.detail.status;
    const hidden = e.detail && e.detail.hidden;
    if (status === 'background') {
      canShowAd = true;
    }
    if (Number(hidden) === 0 && canShowAd && !showFirstAd) {
      showFirstAd = true;
      alitripBridge.call('taku_ad_plugin', {
        action: "showInterstitialAD",
        placement_id: placementId,
        open_uid: '',
        server_data: ''
      }, function (ret) {
        alitripBridge.call('toUT', getLogParams({
          logkey: '/tbtrip.taku-sdk.show'
        }));
      }, function (e) {
        alitripBridge.call('toUT', getLogParams({
          logkey: '/tbtrip.taku-sdk.showerror'
        }));
      });
    }
  })
  </script>`