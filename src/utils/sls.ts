const ALY = require('aliyun-sdk');
const slsClient = new ALY.SLS({
  accessKeyId: 'LTAI5tDwKExnH8vud8AB1DGw', //阿里云访问密钥AccessKey ID。更多信息，请参见访问密钥。阿里云账号AccessKey拥有所有API的访问权限，风险很高。强烈建议您创建并使用RAM用户进行API访问或日常运维。
  secretAccessKey: '******************************', //阿里云访问密钥AccessKey Secret。
  endpoint: 'http://cn-zhangjiakou.log.aliyuncs.com', //日志服务的域名。更多信息，请参见服务入口。此处以杭州为例，其它地域请根据实际情况填写。
  apiVersion: '2015-06-01', //SDK版本号，固定值。
});
const projectName = 'flproxy'; // Project名称。
const logStoreName = 'flproxy_log'; // 日志库
const preLogStoreName = 'flproxy_pre_log'; // 预发日志库

export class Sls {
  public ctx;
  constructor(ctx) {
    this.ctx = ctx;
  }

  async putLog(contents) {
    // 阿里云日志入参
    const param = {
      projectName,
      logStoreName: this.ctx.env === 'pre' ? preLogStoreName : logStoreName,
      logGroup: {
        // 必选，写入的日志数据。
        logs: [{
          time: Math.floor(new Date().getTime() / 1000),
          contents,
        }],
        topic: "flproxy",
        source: "flproxy"
      }
    };
    // 上报阿里云日志
    slsClient.putLogs(param, (err, data) => {
      if (err) {
        console.error('error:', err);
      } else {
        console.log('写入日志成功', data);
      }
    });
  }
}
