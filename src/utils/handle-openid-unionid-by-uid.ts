import { FaaSHTTPContext } from '@midwayjs/faas';
// import * as urllib from 'urllib';
// import * as KeyCenterClient from '@ali/keycenter';
// import { putSlsLog } from './index'

/**
 * 微信小程序透传UnionId的Cookie的key
 */
const WX_COOKIE_UNION_ID_KEY = '_fm_wx_union_id_';
/**
 * 微信小程序透传OpenId的Cookie的key
 */
const WX_COOKIE_OPEN_ID_KEY = '_fm_wx_open_id_';

function removeCookie(ctx: FaaSHTTPContext, name: string, domain: string) {
  // 存在则删除，不存在不需要处理
  if (ctx.cookies.get(name)) {
    ctx.cookies.set(name, '', { maxAge: -1, domain });
  }
}

// 清除登录用户信息
export function removeLoginInfo(ctx: FaaSHTTPContext) {
  let domain = ctx.host;
  try {
    domain = domain.split('.').slice(-2).join('.');
  } catch (e) { }
  const cookies = ctx.response.get('set-cookie');
  // @ts-ignore
  ctx.response.set('set-cookie', (cookies || []).filter(item => item.indexOf('_tb_token_') === -1));
  removeCookie(ctx, 'munb', domain);
  removeCookie(ctx, 'unb', domain);
  removeCookie(ctx, 'unb', domain);
  removeCookie(ctx, '_tb_token_', domain); // 套壳根据此字段标识是否已登录
  removeCookie(ctx, WX_COOKIE_UNION_ID_KEY, domain);
  removeCookie(ctx, WX_COOKIE_OPEN_ID_KEY, domain); 
}