import { OutgoingHttpHeaders } from 'http';
import { Context } from '@midwayjs/faas';
import { SSR_HOST_ARR, NO_OVERRIDE_HEADERS } from './constant';
import { Sls } from './sls';
import { IMAGE_EXTENSION } from './constant';
import { getFileExtension } from './utils';
import { getFromRemote } from './req-resource';

/**
   * 上报阿里云日志
   */
const putSlsLog = (context, contents) => {
  let realContents = contents;
  if (!realContents) return;

  if (Object.prototype.toString.call(realContents) === '[object Object]') {
    realContents = objToArr(realContents);
  }
  const sls = new Sls(context);
  try {
    sls.putLog(realContents.concat({ key: 'traceId', value: context.eagleeye?.traceId }));
  } catch (err) {
    context.logger.error('上报阿里云日志失败');
  }
}

// 将对象数据转换成sls需要的数据类型数据，用于上报
const objToArr = (obj) => Object.entries(obj).map(([key, value]) => ({ key, value: String(value) }));

// 判断是不是ssr链接
const SSRPathTest = (host) => {
  return SSR_HOST_ARR.includes(host);
}

/**
 * 轮询获取tair数据
 * @method getData
 * @param {String} tairKey 获取tair的key
 * @param {Object} tairClient tair应用
 * @param {Number} intervalNum 轮询间隔，单位ms
 * @returns {*}
 */
const intervalGetTairData = async (tairKey: string, tairClient: any, intervalNum: number = 100, maxTime: number = 1000, context: any): Promise<any> => {
  return new Promise((resolve) => {
    let count = 0;
    const intervalId = setInterval(async () => {
      try {
        const getTairStartTime = Date.now();
        // 轮询获取 Tair 数据，此处使用 null 代替真实数据
        const tairData = await tairClient.get(tairKey);
        if (!tairData || !tairData.data) clearInterval(intervalId);
        const getTairUseTime = Date.now() - getTairStartTime;
        const parsedTairData = JSON.parse(tairData.data);

        // 如果获取到了 Tair 数据，清除定时器并 resolve Promise
        if (parsedTairData && parsedTairData.status && parsedTairData.status === 'end') {
          clearInterval(intervalId);
          resolve({
            pageData: JSON.parse(parsedTairData.data),
            getTairUseTime
          });
        }

        // 每次执行计数器自增，如果计数器达到指定的次数，清除定时器
        count++;
        if (count * intervalNum >= maxTime) {
          clearInterval(intervalId);
          resolve(null);
        }
      } catch (error) {
        context.logger.log(`资源预取gettair报错,key=${tairKey},error=${JSON.stringify(error)}`);
        clearInterval(intervalId);
      }
    }, intervalNum);
  });
}

/**
 * 获取page html，cache和重新请求竞速
 * @method getPageResponseRace
 * @param {String} fetchPath 需要获取的页面路径
 * @param {Object} airClient airClient
 * @param {Object} tairClient tair应用
 * @param {Object} userAgent userAgent
 * @param {Object} fetchPathQuery fetchPathQuery
 * @returns {*}
 */
const getPageResponseRace = async ({ fetchPath, context, airClient, tairClient, isSSRHost, path, rawRequest }): Promise<any> => {
  const ext = getFileExtension(path);
  // 图片资源代理、
  if (IMAGE_EXTENSION.includes(ext) || rawRequest) {
    return getFromRemote(fetchPath, context);
  }

  // er 场景加上请求头
  if (isSSRHost && context.request.headers) {
    context.request.headers['wx-proxy-host'] = context.hostname;
  }

  // 20ms轮询取tair缓存 以及 重新请求 谁快取谁
  const fetchStartTime = Date.now();
  const pageResponse = await Promise.race([new Promise(async (resolve) => {
    const pageData = await airClient.getPage({
      pageUrl: decodeURIComponent(fetchPath),
      headers: context.request.headers,
    });
    putSlsLog(context, {
      logType: 'getPageLog',
      type: 'getPageByAir',
      path,
      isSSRHost,
      pageUrl: decodeURIComponent(fetchPath),
      fromCache: context.query._fz_web_from_base_cache || false,
      useTime: Date.now() - fetchStartTime,
      nowTime: getLogDate(),
      tairUseTime: 0
    });
    resolve(pageData);
  }), new Promise(async (resolve) => {
    if (context.query && context.query._fz_web_base_cache_key && isSSRHost) {
      if (context.query._fz_web_base_cache_key.length < 100) {
        const tairData = await intervalGetTairData(context.query._fz_web_base_cache_key, tairClient, 20, 1000, context);
        const pageData = tairData?.pageData;
        const tairUseTime = tairData?.getTairUseTime;
        if (pageData) {
          putSlsLog(context, {
            logType: 'getPageLog',
            type: 'getPageByCache',
            path,
            isSSRHost,
            pageUrl: decodeURIComponent(fetchPath),
            fromCache: context.query._fz_web_from_base_cache || false,
            useTime: Date.now() - fetchStartTime,
            nowTime: getLogDate(),
            tairUseTime
          });
          resolve(pageData);
        }
      } else {
        context.logger.log(`cacheKey过长,key=${context.query._fz_web_base_cache_key}`);
      }
    }
  })]);
  putSlsLog(context, {
    logType: 'getPageLog',
    type: 'getPage',
    path,
    isSSRHost,
    pageUrl: decodeURIComponent(fetchPath),
    fromCache: context.query._fz_web_from_base_cache || false,
    useTime: Date.now() - fetchStartTime,
    nowTime: getLogDate()
  });

  return pageResponse;
}

// 基于当前时间 生成 YYYY-MM-DD格式的日期数据
const getLogDate = () => {
  const date = new Date();
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  return `${year}-${month < 10? `0${month}` : month}-${day < 10? `0${day}` : day}`;
}


/**
 * setResponseHeaders 设置http响应头
 *
 * @method setResponseHeaders
 * @param {Object} pageResponse 响应头
 * @param {Context} ctx 请求上下文
 */
  const setResponseHeaders = (pageResponse: any, ctx: Context) => {
    const responseHeaders: OutgoingHttpHeaders = pageResponse.headers;
    Object.entries(responseHeaders).forEach(([headerKey, headerValue]) => {
      // 对于部分响应头，不进行覆写
      if (NO_OVERRIDE_HEADERS.indexOf(headerKey) >= 0) return;
      // 处理重定向链接
      ctx.response.set(headerKey, typeof headerValue === 'number' ? String(headerValue) : headerValue);
    });
  }

export {
  SSRPathTest,
  getPageResponseRace,
  putSlsLog,
  getLogDate,
  setResponseHeaders
}