import { Context, FaaSHTTPContext } from '@midwayjs/faas';
import { SSRPathTest } from './index';
import {
  PRO,
  PRE,
  PASSPORT_LOGOUT_INFO,
  PCRAFT_PATH_INFO,
  SCRM_HOST,
  PRO_DOMAIN_WHITELIST,
} from './constant';

const FM_REAL_HOST_KEY = '_fm_real_host_';

const getFetchHost = (ctx: Context, firstPath: string, isWapa: boolean, isSSRHost: boolean) => {
   // 使用使用线上域名代理预发或本地资源
   const {
    _fm_pro_proxyany_: fliggyProProxyAny,
    [FM_REAL_HOST_KEY]: fmRealHost,
    _fm_use_edith_: useEdith,
    _fm_project_version_: edithProjectVersion,
  } = ctx.query || {};
  // 环境判断是预发或者强调一定访问预发资源（解决抖音等小程序场景套壳H5域名不能用预发）
  if (isWapa || fliggyProProxyAny) {
    // edith域名切分支
    if (useEdith && edithProjectVersion) {
      ctx.query.projectVersion = edithProjectVersion;
      return isWapa ? 'pre-edith.feizhu.com' : 'edith.feizhu.com';
    }
    // 代理预发host
    const preHost = PRE[firstPath];
    if (preHost && !isSSRHost) {
      return preHost;
    }
  }

  // 优先取链接上有带源host
  if (fmRealHost) {
    // 如果是线上，则只允许白名单的源host
    if (isWapa || PRO_DOMAIN_WHITELIST.indexOf(fmRealHost) > -1) {
      return fmRealHost;
    }
  }

  // 未带源host，则取可代理的path
  const realHost = PRO[firstPath];
  if (realHost) {
    return realHost;
  }
  // 上面逻辑取不到，兜底取cookie
  return ctx.cookies.get(FM_REAL_HOST_KEY);
}

/** 获取真实域名 */
export function getRealHost(ctx: Context, firstPath: string) {
  // 使用使用线上域名代理预发或本地资源
  const { [FM_REAL_HOST_KEY]: fmRealHost } = ctx.query || {};
  // 代理原链接host
  const isSSRHost = SSRPathTest(fmRealHost);
  const isWapa = ctx.proxyEnv === 'wapa';
  const realHost = getFetchHost(ctx, firstPath, isWapa, isSSRHost);

  return {
    isSSRHost,
    realHost,
  };
}

/** 往cookie中注入真实域名 */
export function setFmRealHostCookie(cookies: FaaSHTTPContext['cookies'], host: string, query: Record<string, string>) {
  const fmRealHost = query[FM_REAL_HOST_KEY]
  if (!query || !query[FM_REAL_HOST_KEY]) {
    return;
  }
  // 尝试将cookie同步到二级域名
  let domain = host;
  try {
    domain = host.split('.').slice(-2).join('.');
  } catch (e) {}

  cookies.set(FM_REAL_HOST_KEY, fmRealHost, {
    domain: domain,
    maxAge: 60 * 60, // 仅保1小时
    httpOnly: true,
  });
}

/** 拼接页面参数 */
export function setPageQuery(fetchPath: string, { query, firstPath, rawRequest, isSSRHost }) {
  const useEdith = query && query._fm_use_edith_;
  const edithProjectVersion = query && query._fm_project_version_;
  const whPid = query && query[PCRAFT_PATH_INFO.queryKey];

  // 需要带上页面参数的场景：ssr、开发切换版本、退出登录页
  const addPageQuery = isSSRHost || (useEdith && edithProjectVersion) || rawRequest;
  const searchParams = new URLSearchParams(addPageQuery ? query : '');

  // 老逻辑：删除此字段
  searchParams.delete('__back_url');
  // 删除微信开启流式的参数（此链路不支持流式）
  searchParams.delete('wx_er_stream');

  // 页匠页带上wh_pid，拉取最新bundle
  if (firstPath === PCRAFT_PATH_INFO.path && whPid) {
    searchParams.set(PCRAFT_PATH_INFO.queryKey, whPid);
  }

  // 获取最终请求携带的参数
  const paramsStr = searchParams.toString();
  const queryStr = paramsStr ? `?${paramsStr}` : '';

  return `${fetchPath}${queryStr}`;
}

/** 获取请求资源路径信息 */
export function getFetchPathInfo(ctx: Context) {
  const { path } = ctx;
  const protocol = 'https://';
  const key = /^\/([a-z\.\-])+/g.exec(path);
  const firstPath = key ? key[0] : ''; // 一级路径

  const { realHost, isSSRHost } = getRealHost(ctx, firstPath);

  if (!realHost) {
    return {
      fetchPath: '',
    };
  }
  const url = protocol + realHost + path;
  // 是否是登出链接
  const isPassportLogout = PASSPORT_LOGOUT_INFO.HOST.indexOf(realHost) > -1 && path === PASSPORT_LOGOUT_INFO.PATH;
  const isScrm = SCRM_HOST.indexOf(realHost) > -1;
  const rawRequest = isPassportLogout || isScrm;
  // 设置页面参数
  const fetchPath = setPageQuery(url, { query: ctx.query, firstPath, isSSRHost, rawRequest });

  return  {
    fetchPath,
    isSSRHost,
    isPassportLogout,
    firstPath,
    protocol,
    rawRequest,
  };
}