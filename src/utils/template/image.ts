const escape = require('escape-html');

/** 图片资源代理模板 */
export function getImageHtml({ url, title }) {
  return `<!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="utf-8">
        <title>${title}</title>
        <meta name="aplus-terminal" content="1" />
        <meta name="aplus-waiting" content="MAN" />
        <meta name="weex-viewport" content="750" />
        <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no, viewport-fit=cover">
        <meta name="data-spm" content="181.24178638"/>
        <meta name="page-name" content="rx-faas-newcomer_newcomer_index"/>
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="black" />
        <meta name="apple-touch-fullscreen" content="yes" />
        <meta name="format-detection" content="telephone=no, email=no" />
    </head>
    <style>
      html, body {
        -ms-overflow-style: scrollbar;
        -webkit-tap-highlight-color: transparent;
        padding: 0;
        margin: 0;
        width: 100%;
        height: 100%;
        min-height: 100vh;
      }

      html {
        overflow: hidden;
      }

      body {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        flex-direction: column;
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        margin: 0;
      }

      div, a, img {
        -webkit-tap-highlight-color: transparent;
        -webkit-touch-callout: none;
        -webkit-user-select: none;
      }

      input[type="search"]::-webkit-search-decoration,
      input[type="search"]::-webkit-search-cancel-button {
        -webkit-appearance: none !important;
      }
      body [web-sticky] {
        position: -webkit-sticky !important;
        position: sticky !important;
      }
        .image {
            width: 100%;
            height: 100%;
        }
    </style>
    <body>
        <img src="${escape(url)}" class="image" />
    </body>
    </html>
  `;
}