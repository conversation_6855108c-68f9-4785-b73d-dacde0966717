/**
 * typeOf
 *
 * 获取入参类型的string
 *
 * - undefined将返回'undefined'
 * - null将返回'null'
 * - 其他均返回类型Object.prototype.toString.call后的后8位，例如：string，array等
 */
export function typeOf(v?: any): string {
  if (typeof v === 'undefined') {
    return 'undefined';
  } else if (v === null) {
    return 'null';
  } else {
    return Object.prototype.toString.call(v).slice(8, -1).toLowerCase();
  }
}

/**
 * isValue，当前是不是一个有效值
 *
 * - 数字类型，返回isFinite
 * - null和undefined返回false
 * - 其他返回!!v
 */
export function isValue(v: any): boolean {
  let t = typeOf(v);

  switch (t) {
    case 'number':
      return isFinite(v);
    case 'null':
    case 'undefined':
      return false;
    default:
      return !!t;
  }
}

/**
 * 获取某个路径的数据
 * @method getData
 * @param {Object} data 当前数据对象
 * @param {String} path 数据路径 a.b.c[2].d[e]
 * @param {*} defaultValue 当数据路径没有值的时候，返回 defaultValue
 * @returns {*}
 */
export function getData(data: any, path: string, defaultValue?: any): any {
  let ret = defaultValue;

  if (data && path) {
    let keys = path.match(/([^\.\[\]"']+)/g);
    let d = data;

    keys.every((key: any) => {
      /^\d+$/.test(key) && (key = Number(key));
      d = d[key];

      if (isValue(d)) {
        ret = d;
        return true;
      } else {
        ret = defaultValue;
        return false;
      }
    });
  }

  return ret;
}



/**
 * getFileExtension 获取文件的后缀名
 *
 * @method getFileExtension
 * @param {String} filename 文件路径
 * @returns {String} 返回文件的后缀名
 */
export function getFileExtension(filename) {
  // 确保传入的是字符串，并且包含点号（.）
  if (filename && typeof filename === 'string' && filename.includes('.')) {
    const res = filename.split('.').pop();
    return res ? `.${res}` : '';
  }
  return '';
}
