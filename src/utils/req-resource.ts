import urllib, { HttpClientResponse } from "urllib";

  /**
   * 对网页发起请求
   * @param {String} fetchPath 请求链接
   * @param {Object} context 请求上下文
   * @returns 请求结果
   */
  export async function getFromRemote(fetchPath: string, context: any) {
    // 覆盖请求头的相关参数
    const pageData = {
      pageUrl: decodeURIComponent(fetchPath),
      headers: context.request.headers,
    }
    let result: HttpClientResponse<any>;
    try {
      result = await urllib.request(pageData.pageUrl, pageData.headers);
    } catch (e) {
      if (e.name === "ConnectionTimeoutError") {
        // 超时重试一次
        result = await urllib.request(pageData.pageUrl, pageData.headers);
      } else {
        throw e;
      }
    }
    return {
      content: result?.data,
      statusCode: result?.status,
      headers: result?.headers,
      noParse: true,
    };
  }