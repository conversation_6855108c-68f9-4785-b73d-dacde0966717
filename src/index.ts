import { Context } from '@midwayjs/faas';
import * as urllib from 'urllib';
import * as KeyCenterClient from '@ali/keycenter';
import {
  Inject,
  Provide,
  ServerlessFunction,
  ServerlessTrigger,
  ServerlessTriggerType,
} from '@midwayjs/decorator';
import { TairClient } from '@ali/faas-middleware-interface';
import {
  AD_HEADER_SCRIPT,
  AD_BODY_SCRIPT
} from './utils/constant';
import { parse } from 'node-html-parser';
import ShortLinkService from './service/short-link';
import { AirClient } from '@ali/mpi-air-client';
import {
  setProxyResponse,
  ProxySls,
  setUserInfo,
  generateTitanConfNode,
  getWxOffiaccountConfig,
  getXhsUserInfo,
  insertFTest,
  getDisasterHtml,
  getHtmlContent,
} from '@ali/mpi-proxy-utils';

import {
  getPageResponseRace,
  putSlsLog,
  setResponseHeaders
} from './utils/index';
import { removeLoginInfo } from './utils/handle-openid-unionid-by-uid';
import { getFetchPathInfo } from './utils/path';
import AxdistService from './service/axdist';
import { getImageHtml } from './utils/template/image';
import { needRequestScheme } from './utils/url-scheme';

let tairClient: TairClient;

const airClient = new AirClient({
  cacheLimit: 300, // {number} 页面缓存最大数量
  timeout: 5000, // {number} 请求超时时间，单位毫秒
  cachePath: 'airClientCache', // {string} 缓存位置
  cacheExpires: 60 * 1000 // {number} 缓存失效时间，单位毫秒
});

const noCacheAirClient = new AirClient({
  cacheLimit: 0, // {number} 页面缓存最大数量
  timeout: 5000, // {number} 请求超时时间，单位毫秒
  cachePath: 'airClientNoCache', // {string} 缓存位置
  cacheExpires: 0 // {number} 缓存失效时间，单位毫秒
});

@Provide()
export class IndexHandler {

  @Inject()
  ctx: Context;

  @Inject()
  serviceManager;

  @Inject()
  shortLinkService: ShortLinkService;

  @Inject()
  axdistService: AxdistService;

  centerClient = new KeyCenterClient.default({
    // app发放码，需要到控制台申请，流程参加：https://yuque.antfin-inc.com/mkf08w/cvdlr6/aone-encrypt-scene
    appNum: '62e430b0aef640cb9f64d70a6d46af20',
    // 和keycenter服务器的信道是否加密,默认加密
    encryptCommunication: true,
    urllib,
    protocol: 'http',
    // 详情参考：https://yuque.antfin-inc.com/mkf08w/cvdlr6/environment
    url: 'http://keycenter-service.alibaba-inc.com/keycenter',
  })

  getTairClient(): TairClient {
    if (tairClient) return tairClient;
    try {
      tairClient = this.ctx.tairManager.getClient({ username: '1b624e98ce3e4273' });
    } catch (e) {
      this.putSlsLog({
        logType: 'error',
        errorType: 'getTairClient',
        errInfo: e,
      });
    }
    return tairClient;
  }

  /**
   * 上报阿里云日志
   */
  putSlsLog(contents) {
    putSlsLog(this.ctx, contents);
  }


  getProxySls() {
    return new ProxySls({
      ctx: this.ctx,
      projectName: 'fl-weixin-proxy',
    });
  }

  /**
   * 静态资源代理
   */
  @ServerlessFunction({ functionName: 'staticAgent' })
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/proxy',
    method: 'get',
  })
  async staticAgent() {
    const { query } = this.ctx.request;
    const { url, title = '' } = query;
    const _url = decodeURIComponent(url);
    let hostname = '';

    // url校验
    try {
      hostname = new URL(_url).hostname;
    } catch (err) {
      this.ctx.logger.warn(`[静态资源代理]url不合法:${url}`);
      return '访问链接错误';
    }

    // 域名校验
    if (!/^(gw|img)\.alicdn\.com/i.test(hostname)) {
      this.ctx.logger.warn(`[静态资源代理]不支持的域名:${hostname}`);
      return '访问域名错误';
    }

    return getImageHtml({ url: _url, title });
  }

  /**
   * 代理外部router页，插入广告
   */
  @ServerlessFunction({ functionName: 'staticAgent' })
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/custom',
    method: 'get',
  })
  async customAgent() {
    const { query, headers } = this.ctx.request;
    const { _router_url_ } = query;
    const _url = decodeURIComponent(_router_url_);

    let customRouterUrl = _url;
    
    // 将query中除了_router_url_以外的参数，都覆盖掉customRouterUrl中的参数
    const urlObj = new URL(customRouterUrl);
    // 拼接通用参数
    Object.keys(query).forEach((qKey) => {
      urlObj.searchParams.set(qKey, query[qKey]);
    });
    customRouterUrl = urlObj.toString();

    const pageData = await airClient.getPage({
      pageUrl: customRouterUrl,
      headers,
    });

    // html类型做容灾处理
    const contentType: any = pageData?.headers?.['content-type'];
    if (contentType?.includes('text/html') || contentType?.includes('text/plain')) {
      const statusCodeStr = String(pageData?.statusCode);
      const htmlContent = getDisasterHtml(this.ctx, statusCodeStr);
      // 命中容灾
      if (htmlContent) {
        return htmlContent;
      }
    }

    // 把原返回头的Header复写到现在的请求上，测试场景除外
    if (pageData?.headers) {
      setResponseHeaders(pageData, this.ctx);
    }

    // 如果拉取页面没成功，或者不需要解析内容，则直接将Air结果返回
    if (String(pageData?.statusCode) !== '200') {
      this.ctx.response.status = pageData?.statusCode;
      return pageData?.content;
    }

    let content = String(pageData.content).trim();
    const dom: any = parse(content);
    
    // 修复相对路径资源引用问题
    const scriptTags = dom.querySelectorAll('script[src]');
    const linkTags = dom.querySelectorAll('link[href]');
    const imgTags = dom.querySelectorAll('img[src]');
    
    const fixRelativePath = (element, attrName) => {
      const attrValue = element.getAttribute(attrName);
      // 检查是否是相对路径：以./或../开头，或以/开头但不是//开头，或者不包含://（不是绝对URL）
      // 增加对不带前缀的相对路径的支持，如：assets/index.css?_=123
      if (attrValue && (
          attrValue.startsWith('./') || 
          attrValue.startsWith('../') || 
          (attrValue.startsWith('/') && !attrValue.startsWith('//')) ||
          (!attrValue.includes('://') && !attrValue.startsWith('data:') && !attrValue.startsWith('#'))
        )) {
        try {
          const baseUrl = new URL(_url);
          
          // 分离路径和查询参数
          let path = attrValue;
          let query = '';
          const queryIndex = attrValue.indexOf('?');
          if (queryIndex !== -1) {
            path = attrValue.substring(0, queryIndex);
            query = attrValue.substring(queryIndex);
          }
          
          // 对于不带前缀的路径，使用当前路径作为基础
          const basePath = baseUrl.pathname.substring(0, baseUrl.pathname.lastIndexOf('/') + 1);
          
          // 构建完整URL，确保查询参数被保留
          const fullUrl = new URL(path, baseUrl.origin + (path.startsWith('/') ? '' : basePath));
          element.setAttribute(attrName, fullUrl.href + query);
        } catch (err) {
          this.ctx.logger.warn(`[资源路径修复]修复失败:${attrValue}`);
        }
      }
    };
    
    scriptTags.forEach(script => fixRelativePath(script, 'src'));
    linkTags.forEach(link => fixRelativePath(link, 'href'));
    imgTags.forEach(img => fixRelativePath(img, 'src'));

    let headerScript = parse(AD_HEADER_SCRIPT);
    let bodyScript = parse(AD_BODY_SCRIPT);
    dom.querySelector('head')?.appendChild(headerScript);
    dom.querySelector('body')?.appendChild(bodyScript);

    const domStr = dom.toString();

    return domStr;
  }

  /**
   * 页面代理
   */
  @ServerlessFunction({ functionName: 'agent' })
  @ServerlessTrigger(ServerlessTriggerType.HTTP, { path: '/*', method: 'get' })
  async agent() {
    const sls = this.getProxySls();
    const path = this.ctx.path;
    const unittest = process.env.UNITTEST === 'true'; // 测试场景

    // 特别域名被识别为短链接访问的情况，由于业务代码可能有path的判断，重定向到代理的长链接
    if (/^(pre-|)d\.(fzwxxcx|feizhu)\.com/.test(this.ctx.hostname)) {
      this.putSlsLog({
        logType: 'shortlink',
        path
      });
      return this.shortLinkService.redirectLongUrl(path);
    }
    // 如果是代理域名，且链接上_direct_origin=true，并且不在wx端内，则重定向的_fm_real_host_真实域名链接上
    if (this.ctx.isProxyHostName && this.ctx.query['_direct_origin'] === 'true' && !this.ctx.isWxH5) {
      // 目前给出去的链接没有加needLogin，会导致同步登录态有问题，先直接加上
      const addFlag = `${(this.ctx.url.indexOf('?') > -1 && this.ctx.url.indexOf('needLogin') < 0) ? '&' : '?'}needLogin=true`;

      return this.ctx.redirect(`https://${this.ctx.query['_fm_real_host_'] || 'outfliggys.m.taobao.com'}${this.ctx.url}${addFlag}`)
    }

    // 只有代理域名或者测试场景，才继续代理
    if (!this.ctx.isProxyHostName && !unittest && !this.ctx.query.debugFzProxy) return '访问链接错误';

    // 获取访问资源path等信息
    const { fetchPath, isSSRHost, isPassportLogout, rawRequest } = getFetchPathInfo(this.ctx);
    // 如果是不支持代理的path，直接返回
    if (!fetchPath) {
      this.ctx.logger.warn(`[页面代理]未识别的path：${path}`);
      // 如果链接或cookie中存在host
      this.putSlsLog({
        logType: 'fetchPageError',
        path,
      });
      return '访问链接错误';
    }

    // SSR链接，强依赖登录或有登录态，不走缓存
    // 额外的通过登录态（unb munb）判断是否走缓存；
    const munb = this.ctx.cookies.get('munb');
    const unb = this.ctx.cookies.get('unb');

    const requestAirClient = isSSRHost && (this.ctx.query['needLogin'] || unb || munb) ? noCacheAirClient : airClient;
    sls.performanceTime('getPage');
    const pageResponse = await getPageResponseRace({
      fetchPath,
      context: this.ctx,
      airClient: requestAirClient,
      tairClient: this.getTairClient(),
      isSSRHost,
      path,
      rawRequest,
    }).catch((err) => {
      this.ctx.logger.error(`[页面代理]getPageResponseRace失败，请求路径为${fetchPath}，错误信息：${err}`);
      return {
        noParse: true,
        statusCode: 500,
        content: getHtmlContent('500'),
      };
    });
    sls.performanceTimeEnd('getPage');

    // 把原返回头的Header复写到现在的请求上，测试场景除外
    if (pageResponse?.headers && !unittest) {
      setResponseHeaders(pageResponse, this.ctx);
    }

    // 返回响应头，是否命中缓存
    this.ctx.response.set('x-proxy-hit-cache', pageResponse.fromCache);

    // 如果是退出登录，清除登录信息
    if (isPassportLogout) {
      removeLoginInfo(this.ctx);
    }

    // 不需要解析内容，则直接将Air结果返回
    if (pageResponse?.noParse) {
      this.ctx.response.status = pageResponse?.statusCode;
      return pageResponse?.content;
    }

    // 阿信页面特殊处理
    if (needRequestScheme(path)) {
      const dom: any = parse(String(pageResponse.content).trim());
      await this.axdistService.setUrlScheme(dom);
      return dom.toString();
    }
    
    // 执行jsssk注入、配置注入等
    this.ctx.body = pageResponse?.content || '';
    this.ctx.response.status = pageResponse?.statusCode;
    const pageContent = await setProxyResponse(
      this.ctx,
      sls,
      () => this.getTairClient(),
      this.centerClient
    ).catch((err) => {
      this.ctx.logger.error(`[页面代理]setProxyResponse失败: ${err}`);
      return null;
    });
    return pageContent || pageResponse?.content;
  }

  /**
   * 获取套壳异步信息（提供给er服务使用）
   */
  @ServerlessFunction({ functionName: 'webviewInfo' })
  @ServerlessTrigger(ServerlessTriggerType.HTTP, { path: '/miniprogram/webview-info', method: 'post' })
  async getWebviewInfo() {
    try {
      const scriptList = [];
      const { pageUrl } = this.ctx.request.body;
    

      if (!pageUrl) {
        return {
          success: false,
          scriptList,
          msg: '缺失pageUrl参数'
        }
      }
      const sls = this.getProxySls();
      const pageUrlObj = new URL(pageUrl);

      if (this.ctx.isWxH5) {
        // 微信公众号配置、泰坦配置（降级、直连ER）、微信账号信息
        await Promise.all([
          getWxOffiaccountConfig(this.ctx, { pageUrl, scriptList, sls }),
          generateTitanConfNode(this.ctx, this.getTairClient(), sls, scriptList),
          setUserInfo(this.ctx, {
            centerClient: this.centerClient,
            sls,
            scriptList,
            pageHost: pageUrlObj.host,
          }),
        ]);
      } else if (this.ctx.isXhsH5) {
        // 小红书账号信息
        const xhsUserScript = await getXhsUserInfo(this.ctx, sls);
        scriptList.push(xhsUserScript);
      }
      // ATOM注入
      insertFTest(this.ctx, scriptList, sls);
      sls.performanceEnd('webviewInfo', { pageUrl })
      return {
        success: true,
        scriptList,
      };
    } catch (e) {
      return {
        success: false,
        msg: e,
      };
    }
  }
  /**
   * 上报sls日志
   */
  @ServerlessFunction({ functionName: 'proxyLog' })
  @ServerlessTrigger(ServerlessTriggerType.HTTP, { path: '/miniprogram/proxy-log', method: 'post' })
  async sendSlsLog() {
    const { data } = this.ctx.request.body;
    
    if (data) {
      const { logType, ...other } = data ;
      const sls = this.getProxySls();
      sls.putSlsLog(logType || 'httpLog', {
        ...other,
        isHttpLog: true,
      });
    }
    
    return {
      success: true,
    }
  }
}
