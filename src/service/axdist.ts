import { Context } from '@midwayjs/faas';
import { Inject, Provide } from '@midwayjs/decorator';
import { parse } from 'node-html-parser';
import { getSchemeRequestParams, needRequestScheme, getUrlSchemeScript } from '../utils/url-scheme';

@Provide()
export default class AxdistService {
  @Inject()
  ctx!: Context;

  /**
   * 设置
   * @param shortKey 
   */
  async setUrlScheme(dom): Promise<void> {
    // 中转页提前获取urlScheme，解决mTop令牌过期设置cookies不稳定问题
    try {
      if (needRequestScheme(this.ctx.path)) {
        const params = getSchemeRequestParams(this.ctx.query);
        const res = await this.ctx.hsfClient.invoke<{ model: string }>({
          id: 'com.alibaba.tripzoo.proxy.api.service.WxMiniProgramService:1.0.0',
          group: 'HSF',
          method: 'getUrlScheme',
          parameterTypes: ['com.alibaba.tripzoo.proxy.request.WxUrlSchemeRequest'],
          args: [{
            WxJumpWxaBO: params,
            neverExpire: false,
            availableDays: 31,
          }],
        });

        if (res?.model) {
          const _script = parse(getUrlSchemeScript(res.model));
          dom.querySelector('body')?.appendChild(_script);
        }
      }
    } catch (err) {
      console.error(err)
    }
  }
}