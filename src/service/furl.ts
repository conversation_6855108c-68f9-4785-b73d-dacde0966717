
import { Inject, Provide } from '@midwayjs/decorator';
import { FaaSContext } from '@midwayjs/faas';

@Provide()
export default class FurlService {
  @Inject()
  ctx!: FaaSContext;

  /**
   * 查询短链信息
   * @param shortPath 短链path，类似1d3nL6
   * @returns 
   */
  async queryFurl(shortPath: string) {
    const res: any = await this.ctx.hsfClient.invoke({
      group: 'HSF',
      id: 'com.alibaba.fliggy.furl.service.FurlService:1.0.0',
      method: 'queryFurl',
      parameterTypes: ['com.alibaba.fliggy.furl.model.FurlVisitModel'],
      args: [{ shortPath }]
    });

    if (!res?.data?.longLink) {
      throw Error(`FurlService:queryFurl by ${shortPath} failed`);
    }

    return res.data as IShortLinkInfo;
  }
}

export interface IShortLinkInfo {
  /** 短链 */
  shortKey: string;
  /** 长链 */
  longLink: string;
}