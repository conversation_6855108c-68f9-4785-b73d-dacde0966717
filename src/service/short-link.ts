import { Context } from '@midwayjs/faas';
import { Inject, Provide } from '@midwayjs/decorator';
import FurlService from './furl';

@Provide()
export default class ShortLinkService {
  @Inject()
  ctx!: Context;

  @Inject()
  furlService: FurlService;

  /**
   * 根据短链重定向到对应长链（代码从agent函数迁移过来）
   * @param urlPath 短链路径
   */
  async redirectLongUrl(urlPath: string) {
    const pathKeys = urlPath.split('/');
    const shortKey = pathKeys[1];
    // 解决短链请求过长，tair报错的问题
    if (shortKey && shortKey.length > 100) {
      this.ctx.logger.warn(`短链接服务shortKey过长,key=${shortKey},url=${this.ctx.url}`);
      this.ctx.redirect('https://market.m.taobao.com/app/trip/rx-home/pages/home');
      return;
    }
    // 兼容短信打开存在带其他字符的情况
    let key = shortKey.length > 6 ? shortKey.substr(0, 6) : shortKey;
  
    const serviceShortUrlMsg = await this.furlService.queryFurl(key).catch(err => {
      this.ctx.logger.error(`短链接服务queryFurl失败 ,key=${key},url=${this.ctx.url},错误信息:${err}`);
      return null;
    });
    const longLink = serviceShortUrlMsg?.longLink;

    // 出错情况下的处理,重定向到飞猪首页
    if (!serviceShortUrlMsg || !longLink) {
      this.ctx.logger.error(`短链接服务发生错误,key=${shortKey},url=${this.ctx.url}`);
      this.ctx.redirect('https://market.m.taobao.com/app/trip/rx-home/pages/home');
      return;
    }
    
    this.ctx.redirect(longLink);
    return;
  }
}