import { join } from 'path';
import { ILifeCycle } from '@midwayjs/core';
import { Config, Configuration, App } from '@midwayjs/decorator';
import * as faas from '@midwayjs/faas';

@Configuration({
  importConfigs: [join(__dirname, './config/')],
  imports: [],
})
export class ContainerConfiguration implements ILifeCycle {
  @Config('sequelize')
  sequelizeConfig: any;

  @App()
  app: faas.Application;

  // @ts-ignore
  async onReady(): Promise<void> {
    console.log('进入ready>>>>>>>>', this.sequelizeConfig);
    // 建立数据库连接
    // try {
    //   await DB.init(this.sequelizeConfig);
    // } catch (e) {
    //   console.log('DB init error', e)
    // }
  }
}
