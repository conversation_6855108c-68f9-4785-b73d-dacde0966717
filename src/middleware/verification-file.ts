import { Provide, ScopeEnum, Scope } from '@midwayjs/decorator';
import { Context } from '@midwayjs/faas';
import { JS_DOMAIN, BIZ_DOMAIN } from '../utils/constant';

@Provide('verificationFile')
@Scope(ScopeEnum.Singleton)
export class VerificationFileMiddleware {
  resolve() {
    return async (ctx: Context, next) => {
      const path = ctx.request.path;
      // 拆分路径和文件名
      const splitLoc = path.lastIndexOf('/');
      const file = path.slice(splitLoc + 1);
      const fileContent = BIZ_DOMAIN[file] || JS_DOMAIN[file];
      // 有值则说明命中校验文件，则直接返回      
      if (fileContent) {
        ctx.body = fileContent;
        return;
      }

      return await next();
    };
  }
}
