/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/7/14
 * @modify 2021/7/14
 */
import { Provide, ScopeEnum, Scope } from '@midwayjs/decorator';
import { Context } from '@midwayjs/faas';
import {
    PRE_HOST_NAMES,
    PRO_HOST_NAMES
} from '../utils/constant';

@Provide('appOriginConfig')
@Scope(ScopeEnum.Singleton)
export class AppOriginConfig {
    resolve(){
        return async (ctx: Context, next: Function) => {
            const hostname = ctx.hostname;
            const ua = ctx.headers['user-agent'] || '';
            ctx.isProxyHostName = PRE_HOST_NAMES.concat(PRO_HOST_NAMES).includes(hostname);
            ctx.proxyEnv = PRE_HOST_NAMES.includes(hostname) ? 'wapa' : 'm';
            ctx.proxyHostname = ctx.isProxyHostName ? hostname : PRO_HOST_NAMES[0];
            ctx.isWxH5 = !!ua.match(/MicroMessenger/i);
            ctx.isTtH5 = !!ua.match(/ToutiaoMicroApp/i);
            ctx.isXhsH5 = !!ua.match(/xhsminiapp/i);
            ctx.set('Access-Control-Allow-Origin', `https://${ctx.proxyHostname}`);
            ctx.set('Access-Control-Allow-Methods', 'POST, GET, OPTIONS');
            ctx.set('Access-Control-Allow-Headers', 'Content-Type');
            ctx.set('Access-Control-Allow-Credentials', 'true');

            await next();
            return;
        }
    }
}
