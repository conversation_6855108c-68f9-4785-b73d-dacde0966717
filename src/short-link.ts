import {
  Inject,
  Provide,
  ServerlessFunction,
  ServerlessTrigger,
  ServerlessTriggerType,
} from '@midwayjs/decorator';
import { Context } from '@midwayjs/faas';
import FurlService from './service/furl';

@Provide()
export class ShortLinkHandler {

  @Inject()
  ctx: Context;

  @Inject()
  serviceManager;

  @Inject()
  furlService: FurlService;

  /**
   * 发布为 hsf 时
   * 这个参数是 ginkgo 固定的，入参出参都为字符串
   * @param event
   */
  @ServerlessFunction({ functionName: 'short-link' })
  @ServerlessTrigger(ServerlessTriggerType.HSF)
  async handler(event: any = {}): Promise<any> {
    // 传needForever存储到数据库，否则tair缓存1年
    const { longLink, needForever, requestAppName = 'fl-weixin-proxy' } = event;
    this.ctx.logger.warn(`短链生成，eagleeyeid=${this.ctx.eagleeye.traceId},longLink=${longLink}`);
    if (!longLink) return {
      success: false,
      msg: '请传入正确的url'
    };

    // 新服务端链路 @溪居
    let serverShortLink = null;

    const serverShortLinkData = await this.serviceManager.fuFurlServiceCreateFurlService.invoke({ longLink, needForever, requestAppName });
    if (serverShortLinkData && serverShortLinkData.success) {
      serverShortLink = serverShortLinkData?.data?.shortKey;
    }
    if (serverShortLink) {
      return {
        success: true,
        shortLink: serverShortLink
      }
    } else {
      return {
        success: false,
        msg: '生成短链接失败'
      };
    }
  }

   /**
   * 微信内套壳短链重定向
   */
   @ServerlessFunction({ functionName: 'short-url' })
   @ServerlessTrigger(ServerlessTriggerType.HTTP, {
     path: '/short/*',
     method: 'get',
   })
   async shortToLong() {
    const { path, query, isWxH5, host, hostname, isProxyHostName } = this.ctx;
    const matched = path.match(/^\/short\/(.*)/i); // 匹配短链path
    const shortPath = matched?.[1];
    // 如果短链path不存在，则抛出404
    if (!shortPath) {
      this.ctx.status = 404;
      return 'shortPath not exist';
    }
    // 获取长链
    const { longLink } = await this.furlService.queryFurl(shortPath);
    const urlObj = new URL(longLink);
    // 微信端需要转域名并设置参数
    if (isWxH5 && isProxyHostName) {
      const orginHost = urlObj.host;
      urlObj.host = host;
      urlObj.hostname = hostname;
      urlObj.searchParams.set('_fm_real_host_', orginHost);
      Object.keys(query).forEach(queryKey => {
        urlObj.searchParams.set(queryKey, query[queryKey]);
      });
    }
    this.ctx.redirect(urlObj.toString());
  }
}
