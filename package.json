{"name": "fl-weixin-proxy", "widgetName": "fl-weixin-proxy", "version": "1.18.7", "description": "微信小程序H5代理", "main": "src/index.ts", "clam2Version": "1.7.140", "lib": "faas", "group": "func", "repository": {"type": "git", "url": "**************************:func/fl-weixin-proxy.git"}, "keywords": ["fl-weixin-proxy", "serverless", "function", "faas", "func"], "author": {"name": "鼎贤", "email": "<EMAIL>"}, "license": "MIT", "private": true, "dependencies": {"@ali/keycenter": "^2.1.2", "@ali/mpi-air-client": "^1.1.0", "@ali/mpi-proxy-utils": "^1.0.7", "@midwayjs/core": "^2.0.0", "@midwayjs/decorator": "^2.6.4", "@midwayjs/faas": "^2.0.0", "aliyun-sdk": "^1.12.9", "escape-html": "^1.0.3", "mysql2": "^2.1.0", "node-html-parser": "^6.1.3", "sequelize": "^5.21.5", "sequelize-typescript": "^1.1.0", "sha1": "^1.1.1", "urllib": "2.41.0", "uuid": "^8.3.2"}, "devDependencies": {"@ali/midway-cli-plugin-ali": "^1.0.0", "@ali/midway-faas-typings": "^1.0.5", "@ali/serverless-app": "^1.0.0", "@midwayjs/cli": "^1.2.45", "@midwayjs/cli-plugin-faas": "^1.2.45", "@midwayjs/mock": "^2.8.7", "@types/jest": "^26.0.10", "@types/node": "^14", "jest": "^26.4.0", "mwts": "^1.0.5", "ts-jest": "^26.2.0", "typescript": "^4.0.0"}, "publishConfig": {"registry": "http://registry.npm.alibaba-inc.com"}, "toolkit": "@ali/clam-toolkit-faas", "defPublishType": "faas", "commandType": "clam", "midway-cli": {"plugins": ["@ali/midway-cli-plugin-ali"]}, "scripts": {"dev": "midway-bin dev --ts", "test": "midway-bin test --ts"}, "tnpm": {"mode": "npm", "lockfile": "enable"}, "overrides": {"@ali/serverless-rsocket-client": "3.7.2"}}